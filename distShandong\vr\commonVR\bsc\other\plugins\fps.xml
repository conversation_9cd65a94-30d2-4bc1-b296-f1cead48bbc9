<krpano>

	<!--
		fps (frames per second) xml plugin
		- this plugin shows the current average rendering frame-rate
	-->

	<action name="fps_install" autorun="preinit" scope="local">
		delayedcall(1.0,
			addlayer(fps_display);
			set(layer[fps_display],
				type=text,
				keep=true,
				align='lefttop',
				css=calc('font-size:'+(device.mobile AND stagescale LT 1.0 ? 24 : 12)+'px; color:#FFFFFF;'),
				txtshadow='0 0 1 0x000000 1.0',
				bg=false,
				parent=STAGE,
				enabled=false
			);
			setinterval(fps_plugin, 0.5,
				calc(layer[fps_display].html, 'FPS: ' + ((display.currentfps+0.5) BOR 0));
			);
		);
	</action>

</krpano>
