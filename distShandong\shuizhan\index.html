<!DOCTYPE html><html lang=zh-CN><head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><title>数字乡镇云平台</title><meta name=description content=""><link rel=stylesheet href=./style.css><link href=./img/sys-img/loading.webp rel=preload as=image><link href=./img/body-bg.png rel=prefetch as=image><link href=./fonts/DS-DIGI.TTF rel=preload as=font type=font/ttf crossorigin=anonymous><link href=./fonts/acens.ttf rel=preload as=font type=font/ttf crossorigin=anonymous><link href=./fonts/PangMenZhengDao.ttf rel=preload as=font type=font/ttf crossorigin=anonymous><link href=./fonts/digital.ttf rel=preload as=font type=font/ttf crossorigin=anonymous><script src=./js/clappr.min.js></script><style>@font-face {
      font-family: 'PangMenZhengDao';
      src: url('./fonts/PangMenZhengDao.ttf') format('truetype');
    }
    @font-face {
      font-family: 'digital';
      src: url('./fonts/digital.ttf') format('truetype');
    }
    @font-face{
      font-family: 'youshe';
      src: url('./fonts/YouSheBiaoTiHei-2.ttf') format('truetype');
     
    }
    @font-face{
      font-family: 'zcoolwenyiti';
      src: url('./fonts/ZhanKuWenYiTi-2.ttf') format('truetype');
     
    }
    /* 覆盖分页样式 */
    .ant-pagination-prev .ant-pagination-item-link, .ant-pagination-next .ant-pagination-item-link, .ant-pagination-item {
      background: transparent !important;
      border: 1px solid #00FEFE !important;
      color: #00FEFE !important;
    }
    .ant-pagination-item a {
      color: #00FEFE !important;
    }
    .ant-pagination-item-active a, .ant-pagination-item:focus a, .ant-pagination-item:hover a {
      background: #14b3ff !important;
      color: #fff !important;
      border: none !important;
    }
    .ant-pagination-item-active .ant-pagination-item a {
      background: #14b3ff !important;
      color: #fff !important;
      border: none !important;
    }</style><link href=css/chunk-vendors.0545c894.css rel=preload as=style><link href=css/index.54a51258.css rel=preload as=style><link href=js/chunk-vendors.8a0936be.js rel=preload as=script><link href=js/index.d5731776.js rel=preload as=script><link href=css/chunk-vendors.0545c894.css rel=stylesheet><link href=css/index.54a51258.css rel=stylesheet></head><body><div id=appLoading><div id=appLoadingWrapper><div id=appLoadingInfo><span class=appLoadingInfoLine></span><p id=projectName>数字乡镇云平台</p><p id=copyright>山东分公司</p></div><div id=loading-wrapper><div class=loading-gif></div><div id=loading-progress><span class=loading-progress-top-line></span><p class=loading-loading>LOADING...</p><div class=loading-progress-bar><div id=loadingProgressBar class=loading-progress-bar-ani></div></div><div class=loading-progress-content><span id=loadingText class=loading-progress-text>系统正在加载...</span> <span class=loading-progress-num><span id=loadingNum>2</span>%</span></div></div></div><div class=loading-warning id=loadingWarning></div></div></div><div id=app></div><script src=./anov.config.js></script><script src=./anovHelp.js></script><script src=./js/morphsvgplugin.min.js></script><script src=js/chunk-vendors.8a0936be.js></script><script src=js/index.d5731776.js></script></body></html>