{"name": "secondPage", "path": "/secondPage", "leftTop": {"title": "二级标题"}, "leftCenterTop": {"lctTop": {"accessData": [{"name": "需求项总数", "value": 11261, "dimension": "同比", "riseValue": 391}, {"name": "需求满足率", "value": 48.1, "valueUnit": "%", "dimension": "同比", "riseValue": 1.6, "riseUnit": "%"}, {"name": "催办次数", "value": 87, "dimension": "同比", "riseValue": 8}, {"name": "审核通过数", "value": 9556, "dimension": "同比", "riseValue": 30}, {"name": "平均完成时长", "value": 27.8, "valueUnit": "天", "dimension": "同比", "dropValue": 0.8, "dropUnit": "天"}, {"name": "需求超时率", "value": 34.2, "valueUnit": "%", "dimension": "同比", "riseValue": 2.1, "riseUnit": "%"}]}, "lctCenter": {"title": "三级标题", "data": [{"dayValue": 3.5, "value": 12.6, "name": "需求分析"}, {"dayValue": 14.67, "value": 52.8, "name": "数源审核"}, {"dayValue": 3.7, "value": 13.4, "name": "编目挂接"}, {"dayValue": 2.1, "value": 7.6, "name": "需求完善"}, {"dayValue": 3.8, "value": 13.7, "name": "需求实施"}], "colors": ["#2f9eff", "#0754ef", "#29cbe5", "#2af9fa", "#c4fef4"]}, "lctBottom": {"title": "耗时最长部门 TOP3", "tbTitle": ["部门", "环节", "标准用时", "平均用时", "同比"], "data": [{"rank": "TOP 1", "demp": "广东银保监局", "link": "数据部门审核", "standardTime": 5, "avgTime": 71, "scale": 1, "tag": "down"}, {"rank": "TOP 2", "demp": "省交通运输厅", "link": "编目挂接", "standardTime": 5, "avgTime": 66, "scale": 2.6, "tag": "up"}, {"rank": "TOP 3", "demp": "汕头市政数局", "link": "编目挂接", "standardTime": 5, "avgTime": 63.4, "scale": 1.2, "tag": "up"}]}}, "leftCenterBottom": {"titleValue": {"num": 116407, "title": "调用总量"}, "data": [{"value": 116381, "name": "正常调用"}, {"value": 26, "name": "异常调用"}, {"value": 0, "name": "无法调用"}]}, "leftBottom": {"title": "二级标题", "accessData": [{"name": "前置机数", "value": 202}, {"name": "更新及时率", "value": 19, "valueUnit": "%"}, {"name": "检核通过率", "value": 52, "valueUnit": "%"}, {"name": "目录挂接率", "value": 95, "valueUnit": "%"}, {"name": "目录更新率", "value": 20, "valueUnit": "%"}, {"name": "目录规范率", "value": 98, "valueUnit": "%"}]}, "centerTop": [{"name": "重庆", "id": "500000", "value": 824}, {"name": "湖南", "id": "430000", "value": 1072}, {"name": "四川", "id": "510000", "value": 919}, {"name": "广西", "id": "450000", "value": 856}, {"name": "浙江", "id": "330000", "value": 802}, {"name": "广东", "id": "440000", "value": 797}, {"name": "上海", "id": "310000", "value": 296}, {"name": "湖北", "id": "420000", "value": 178}, {"name": "黑龙江", "id": "230000", "value": 627}, {"name": "新疆", "id": "650000", "value": 7.08}, {"name": "甘肃", "id": "620000", "value": 380}, {"name": "天津", "id": "120000", "value": 671}, {"name": "江苏", "id": "320000", "value": 657}, {"name": "北京市", "id": "110000", "value": 634}], "centerBottom": {"leftData": {"title": "三级标题", "accessData": [{"name": "调用量", "value": 10649, "valueUnit": "万条"}, {"name": "支撑事项数", "value": 973, "valueUnit": "项"}, {"name": "活跃接口数占比", "value": "49/311"}, {"name": "调用部门数", "value": 30, "valueUnit": "个"}], "tableData": [{"dept": "深圳市人社局", "usefor": "高校学历查询", "libName": "人口库", "supplyDept": "教育部", "times": "26.6万", "status": 1}, {"dept": "公共资源交易", "usefor": "法人身份信息查询", "libName": "法人库", "supplyDept": "中共广东省委", "times": "22.8万", "status": -1}, {"dept": "省政数局", "usefor": "个体工商户基本信息B", "libName": "法人库", "supplyDept": "省市场监督管理局", "times": "17.9万", "status": 1}, {"dept": "省生态环境厅", "usefor": "企业基本信息L", "libName": "法人库", "supplyDept": "省市场监督管理局", "times": "13.6万", "status": 1}, {"dept": "省人社厅", "usefor": "省常住人口基本信息A", "libName": "人口库", "supplyDept": "广东省公安厅", "times": "11.7万", "status": 1}]}, "rightData": {"title": "三级标题", "chartData": [{"name": "本月", "data": [92, 89, 94, 87, 85]}, {"name": "上月", "data": [90, 86, 87, 82, 83]}]}}, "rightTop": {"title": "二级标题"}, "rightCenterTop": {"accessData": [{"name": "数据治理量", "value": 227922, "valueUnit": "万条"}, {"name": "数据稽核规则", "value": 4093, "valueUnit": "条"}, {"name": "稽核任务总数", "value": 4093, "valueUnit": "次"}, {"name": "问题发现数", "value": 972, "dimension": "同比", "riseValue": 2.1, "riseUnit": "%"}, {"name": "问题解决数", "value": 321}]}, "rightCenterBottom": {"title": "三级标题", "rcbTop": {"accessData": [{"name": "已发布任务", "value": 5}, {"name": "任务平均时长", "value": 35, "valueUnit": "天"}, {"name": "已解决", "value": 3}, {"name": "任务复测率", "value": 33.3, "valueUnit": "%"}]}, "rcbBottom": {"outerData": [{"value": 10.5, "name": "建设运营方"}, {"value": 2.78, "name": "省残联"}, {"value": 0.17, "name": "省教育厅"}, {"value": 7.53, "name": "省市场监管局"}, {"value": 79.02, "name": "其他供数部门"}], "outerColors": ["#2f9eff", "#c4fef4", "#29cbe5", "#2af9fa", "#0754ef"]}}, "rightBottom": {"title": "二级标题", "accessData": [{"name": "数据完整率", "value": 87, "valueUnit": "%", "dimension": "同比", "riseValue": 1.2, "riseUnit": "%"}, {"name": "按时更新率", "value": 19, "valueUnit": "%", "dimension": "同比", "riseValue": 0.5, "riseUnit": "%"}, {"name": "质量综合评价", "value": 86, "valueUnit": "分", "dimension": "同比", "riseValue": 3.2, "riseUnit": "%"}, {"name": "数据合规率", "value": 95, "valueUnit": "%", "dimension": "同比", "riseValue": 1.8, "riseUnit": "%"}, {"name": "问题修复率", "value": 82, "valueUnit": "%", "dimension": "同比", "riseValue": 1.4, "riseUnit": "%"}]}}