!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("common requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,(function(e,t){const o={closeTalk:60,TalkHandshakeReq:59,kFileData:1,kInitDecoderReq:0,kUninitDecoderReq:1,kOpenDecoderReq:2,kCloseDecoderReq:3,kFeedDataReq:4,kStartDecodingReq:5,kPauseDecodingReq:6,kInitDecoderRsp:0,kUninitDecoderRsp:1,kOpenDecoderRsp:2,kCloseDecoderRsp:10,kVideoFrame:4,kAudioFrame:5,kDecodeFinishedEvt:8,cmdCallback:11,kGetParamRsp:12,sendDataCallback:13,socketLinkState:14,getHeartBeatRequest:15,DownloaderHeartBeat:16,closeWebsocket:17,fetchStream:18,resetDecoderFifo:19,getSetPlayBackSpeedReq:20,getSetPlayBackStartReq:21,deviceReady:22,getSetPlayBackStartTimeReq:23,responseCurDateTime:24,eventTypeFifoFull:25,eventTypeFifoEnough:26,playbakcRecordList:27,socketActiveClose:28,serverDataTimeout:29,heartPingResp:30,serverDecoderError:31,socketNoActive:32,getHandsharkReq:33,getHandsharkRes:34,recoderStart:35,recoderPause:36,onWasmLoaded:37,getHandshakeReq:38,DownloaderHandshakeReq:39,sendHandshakeReq:40,kVideoDownRep:100,kVideoDownChumksRep:101,getTalkStartReq:50,talkStartRep:51,pcmDataReq:52,kStartTalkEncoderRsq:53,kStartTalkEncoderRsp:54,kGetDeviceInfo:55,kGetAudioDataReq:56,kgetTalkStartRep:57,DestroyTalkReq:58,kStartTalkEncoderdeviceready:61,_getTalkStartReq:62,serverDecoderTalkError:63,initTalkEncode:64,talkSocketNoActive:65,talkStopResponse:66,talkStopResponse_to_dec:67,talkStopResponse_to_talkWorker:68,DestroyTalkRes:69,PlaybackOver:70};return e.common=o,o}));