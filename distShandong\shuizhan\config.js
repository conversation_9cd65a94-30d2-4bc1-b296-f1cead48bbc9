window.Cesium = Adapter = Cesium = GeoVis || window.GeoVis
var baseImageUrl = '/baseImageUrl/tiles/img/{z}/{x}/{y}.png';
var baseMapUrl = 'http://***************:31140/tiles/map/{z}/{x}/{y}.png';
var darkLayerUrl = "http://***************:31140/tiles/dark/{z}/{x}/{y}.png"
var baseTerrainUrl = "http://***************:31140/tiles/twdem"
var usaTerrainUrl = "http://***************:31140/tiles/usdem"
var geoserverUrl = "http://172.16.10.106:31308/geowebcache"
// var geoserverUrl = "/geowebcache"
var TiandituToken = "91efa2228abb248e65357bf63988ed69"
var sdwebKey = "6a1c3152c5e6a8d697c98b3f9c5785f2" // 山东天地图
// var TiandituToken = "cd61a386eab582f2cda9a7f768bcdb40" 
// 3dtile
// var xinping3dTile = "http://syy.geovisweb.cn:10080/data/3dtile/guizhou/Production_1.json"
var xinping3dTile = "http://***************:31140/tiles/3dtiles/xinping/Production_1.json"
var flv_url2 = 'http://172.16.4.102:8000/rtp/34020000001320010001_34020000001310010001.flv'
var flv_url = 'http://172.16.4.102:8000/rtp/34020000001320010003_34020000001310010003.flv'
var jn3dTile = "http://222.175.160.234:8081/tile-datas/rest/realspace/datas"
var zg3dTile = "http://222.175.160.234:8081/tile-datas/rest/realspace/datas/4a1d5c0d25904196bf6ae5ba24a3d51c/config.scp"
var _minimumZoomDistance = 50 //最小缩放级别
var _maximumZoomDistance = 5000000000000 //最大缩放级别
var paris3dTile = "/3Dtile/tileset.json"
var geoJsonColor = "#86c3ff"
var tilesetColor = "rgba(134, 195, 255,1)"
var bmMode = 'geojson' //3Dtile
var jn3dTile = "/bm-jn/tileset.json"
var WSRdATA = {
    type: 'Feature',
    id: 'v_gis_pt_events_permission_zb.fid--7ea9b77e_18118e2e685_-4f4b',
    geometry: {
        type: 'Point',
        coordinates: [117.12882044636791, 36.720326006460354, 900]
    },
    geometry_name: 'the_geom',
    properties: {
        event_title: '王舍人街道网格'
    }
}
var _ditutype = 'tdt' // 'tdt' 'wms' 'bm'   tdt 天地图   wms 高清图   bm 地图
var _tdtDt = 'img_w'  // 天地图加载的瓦片模型
var _tdtBj = 'cia_w'  // 天地图加载的瓦片标注模型
// 济宁竹竿巷
var onePosition = { "destination": { "x": -2329122.5690949718, "y": 4654307.718522841, "z": 3674718.314137664 }, "orientation": { "heading": 3.860600832014511, "pitch": -0.2823939148561596, "roll": 6.281013949205681 } }
var towPosition = { "destination": { "x": -2328727.605629522, "y": 4654479.5241687335, "z": 3674770.5896582264 }, "orientation": { "heading": 2.75252658133007, "pitch": -0.35963369465603745, "roll": 0.0012825882522591314 } }
// var threePosition = { "destination": { "x": -2334968.1975908875, "y": 4556107.317061037, "z": 3791948.6685010446 }, "orientation": { "heading": 6.211464128779818, "pitch": -0.5140757155618121, "roll": 6.28292018069671 } }
var threePosition = { "destination": { "x": -2329147.4763379768, "y": 4654388.9646045435, "z": 3674685.1819585958 }, "orientation": { "heading": 3.9593336089568867, "pitch": -0.3956640203517714, "roll": 6.28068241196406 } }
var dev_flag = false;
var mountain3dTile = "http://syy.geovisweb.cn:10080/data/3dtile/mountain/Production_1/Scene/Production_1.json"
// tileserver 
var blueVectorLayer = 'http://***************:31140/tiles/FiordColor/{z}/{x}/{y}.png'
var brightVectorLayer = 'http://***************:31140/tiles/Positron/{z}/{x}/{y}.png'
var darkVectorLayer = 'http://***************:31140/tiles/DarkMatter/{z}/{x}/{y}.png'
var basicVectorLayer = 'http://***************:31140/tiles/map/{z}/{x}/{y}.png'
// 截图生成缩略图
var _maximumMemoryUsage = 1024
var _maximum3dTileNumber = 1e10
var _skipLevels = 4
function screenShotTool() {
    setTimeout(() => {
        document.body.onkeypress = e => {
            if (e.key === "G" && e.shiftKey) {
                earth.capture(earth.container, 600).then(canvas => {
                    // console.log(canvas)
                    // var img = canvas.toDataURL("image/png");
                    var filename = "." + location.pathname.split(".")[0] + ".png";
                    fetch("/upload/screen", {
                        method: "post",
                        headers: {
                            "content-type": "application/json"
                        },
                        body: JSON.stringify({
                            filename: filename,
                            file: canvas
                        })
                    })
                        .then(res => res.text())
                        .then(text => console.log(text));
                });
            }
        };
    }, 1300);
}
window["GEOVIS_CAPTURE"] = true;
screenShotTool();