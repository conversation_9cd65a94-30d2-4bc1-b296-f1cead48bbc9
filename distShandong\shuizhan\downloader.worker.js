function Downloader(){this.logger=new Logger("Downloader"),this.ws=null,this.heartbeatTimer=null,this.heartbeatTimes=0}self.importScripts("enumConstant.js","logger.js"),Downloader.prototype.appendBuffer=function(e,t){var o=new Uint8Array(e.byteLength+t.byteLength);return o.set(new Uint8Array(e),0),o.set(new Uint8Array(t),e.byteLength),o.buffer},Downloader.prototype.reportData=function(e){var t={t:common.kFileData,d:e};self.postMessage(t,[t.d])},Downloader.prototype.requestWebsocket=function(e,t){var o,s,a=this;null===this.ws&&(this.ws=new WebSocket(e),this.ws.binaryType="arraybuffer",this.ws.onopen=function(e){a.logger.logInfo("Ws connected."),a.heartbeatTimes=0,s={t:common.sendHandshakeReq},self.postMessage(s)},this.ws.onerror=function(e){a.logger.logError("Ws connect error "+e.data),a.heartbeatTimes=0,a.ws=null},this.ws.onclose=function(e){console.log("websocket 断开"+e.code+" "+e.reason+" "+e.wasClean),clearInterval(a.heartbeatTimer),a.ws=null,o={t:common.socketActiveClose,d:e.code,w:e.wasClean},self.postMessage(o),a.heartbeatTimes=0},this.ws.onmessage=this.onmessage.bind(this))},Downloader.prototype.onmessage=function(e){this.heartbeatTimes=0;var t=e.data;t.byteLength>10485760?console.log("This packet is too big"):self.downloader.reportData(t)},Downloader.prototype.handshakeReq=function(e){this.ws&&this.ws.send(e)},Downloader.prototype.startHeartbeatTimer=function(e){this.ws&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>{console.log(this.heartbeatTimes,"startHeartbeatTimer"),this.ws.send(e),this.heartbeatTimes=this.heartbeatTimes+1,this.heartbeatTimes>3&&(this.ws.close(),clearInterval(this.heartbeatTimer),this.ws=null,req={t:common.socketNoActive},self.postMessage(req),this.heartbeatTimes=0)}),13e3))},Downloader.prototype.closeWebsocket=function(e){this.ws&&(this.ws.close(),clearInterval(this.heartbeatTimer))},Downloader.prototype.setPlayBackSpeed=function(e){this.ws&&this.ws.send(e)},Downloader.prototype.setPlayBackStart=function(e){this.ws&&this.ws.send(e)},Downloader.prototype.SetPlayBackStartTime=function(e){this.ws&&this.ws.send(e)},Downloader.prototype.heartPingResp=function(){this.heartbeatTimes=0},self.downloader=new Downloader,self.onmessage=function(e){if(self.downloader){var t=e.data;switch(t.t){case common.fetchStream:self.downloader.requestWebsocket(t.url,t.msg);break;case common.DownloaderHeartBeat:self.downloader.startHeartbeatTimer(t.b);break;case common.DownloaderHandshakeReq:self.downloader.handshakeReq(t.b);break;case common.closeWebsocket:self.downloader.closeWebsocket();break;case common.getSetPlayBackSpeedReq:self.downloader.setPlayBackSpeed(t.b);break;case common.getSetPlayBackStartReq:self.downloader.setPlayBackStart(t.b);break;case common.getSetPlayBackStartTimeReq:self.downloader.SetPlayBackStartTime(t.b);break;case common.heartPingResp:self.downloader.heartPingResp();break;default:self.downloader.logger.logError("Unsupport messsage "+t.t)}}else console.log("[ER] Downloader not initialized!")};